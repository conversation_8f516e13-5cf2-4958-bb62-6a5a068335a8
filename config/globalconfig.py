import os
from functools import cache
from typing import Type, <PERSON><PERSON>

from pydantic import BaseModel, Field
from pydantic_settings import (
    BaseSettings,
    EnvSettingsSource,
    PydanticBaseSettingsSource,
    SettingsConfigDict,
)

from src.infra.config import TomlConfigByEnvSettingsSource
from src.infra.utils import get_work_dir
from src.schema import const


@cache
def get_or_create_settings_ins() -> "GlobalConfig":
    return GlobalConfig()


class BaseConfig(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", env_nested_delimiter="__")

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> Tuple[PydanticBaseSettingsSource, ...]:
        config_dir = os.path.join(get_work_dir(), "config")
        return (
            init_settings,
            EnvSettingsSource(settings_cls),
            TomlConfigByEnvSettingsSource(settings_cls, config_dir),
        )


class AppSettings(BaseModel):
    name: str = Field(default=const.APP_EN_NAME, description="The name of the setting")
    host: str = Field(default="0.0.0.0", description="The host of the setting")
    log_level: str = Field(default="DEBUG", description="The log level of the setting")
    thread_pool_size: int = Field(
        default=50, description="The thread pool size of the setting"
    )
    http_port: int = Field(default=8888, description="The http port of the setting")
    http_openapi_url: str = Field(
        default="/openapi.json", description="The http openapi url of the setting"
    )
    http_docs_url: str = Field(
        default="/docs", description="The http docs url of the setting"
    )


class LarkClientSetting(BaseModel):
    app_id: str = Field(..., description="Lark app id")
    app_secret: str = Field(..., description="Lark app secret")
    verification_token: str = Field(..., description="Lark verification token")
    encrypt_key: str = Field(..., description="Lark encrypt key")
    user_id: str = Field(..., description="Lark user id")
    system_alert_tmpl_id: str = Field(..., description="Lark system alert template id")
    notice_tmpl_id: str = Field(..., description="Lark notice template id")
    notice_success_tmpl_id: str = Field(..., description="Lark notice success template id")
    notice_fail_tmpl_id: str = Field(..., description="Lark notice fail template id")
    monitor_price_tmpl_id


class LarkSettings(BaseModel):
    trading: LarkClientSetting = Field(
        default_factory=LarkClientSetting, description="Lark trading client setting"
    )
    tt: LarkClientSetting = Field(
        default_factory=LarkClientSetting, description="Lark tt client setting"
    )


class MysqlSettings(BaseModel):
    host: str = Field(..., description="MySQL服务器主机地址")
    port: int = Field(..., description="MySQL服务器端口")
    user: str = Field(..., description="MySQL用户名")
    password: str = Field(..., description="MySQL密码")
    db: str = Field(default="guanfu", description="MySQL数据库名")
    charset: str = Field(default="utf8mb4", description="MySQL字符集")
    conn_timeout: int = Field(default=10, description="连接超时时间(秒)")
    debug: bool = Field(default=False, description="是否开启SQL调试模式")

    # 连接池配置
    pool_size: int = Field(default=8, description="连接池基础大小 (适合1核1G MySQL)")
    max_overflow: int = Field(default=50, description="允许的额外连接数")
    pool_recycle: int = Field(default=1800, description="连接回收时间(秒)")
    pool_timeout: int = Field(default=30, description="获取连接的超时时间(秒)")
    pool_pre_ping: bool = Field(default=True, description="是否在使用前检查连接有效性")


class GlobalConfig(BaseConfig):
    app: AppSettings = Field(default_factory=AppSettings)
    lark: LarkSettings = Field(default_factory=LarkSettings)
    mysql: MysqlSettings = Field(default_factory=MysqlSettings)


if __name__ == "__main__":
    print(GlobalConfig().app)
