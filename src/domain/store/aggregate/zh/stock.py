import concurrent.futures
import threading
import time
from datetime import datetime, timedelta, date
from typing import List, Dict, Any

import pandas as pd
from loguru import logger

from src.domain import communicate
from src.domain.store.aggregate.zh.kline import (
    KLine60MinStoreAggregate,
    KLine30MinStoreAggregate,
    KLineDailyStoreAggregate,
    KLineStoreAggregate,
)
from src.domain.store.repo import dao
from src.domain.schema import MarketTag
from src.infra.app import app
from src.infra.clients.mysql.orm import bulk_insert, bulk_update_v2
from src.infra.clients.trading.ak import AkShareClient
from src.infra.clients.trading.mine import MyClient
from src.infra.clients.trading.schema import PriceUMAUpAction, PriceUMADownAction
from src.infra.clients.trading.utils import (
    get_zh_stock_market,
    transform_row_2_dict,
    get_datetime_range,
)


class ZhStockStoreAggregate:

    def __init__(self):
        self._ak_share_client = AkShareClient()
        self._my_client = MyClient()
        # 初始化各种K线聚合对象
        self.kline_30min = KLine30MinStoreAggregate()
        self.kline_60min = KLine60MinStoreAggregate()
        self.kline_daily = KLineDailyStoreAggregate()
    
    def _update_single_stock_kline(
        self, code: str, start_date: datetime, end_date: datetime
    ):
        """更新单只股票的所有K线数据，确保code和时间/日期唯一，避免重复更新"""
        try:
            # 更新30分钟K线
            logger.info(f"开始更新股票 {code} 的30分钟K线数据")
            self._update_kline_data(
                code, start_date, end_date, self.kline_30min, "datetime"
            )

            # 更新60分钟K线
            logger.info(f"开始更新股票 {code} 的60分钟K线数据")
            self._update_kline_data(
                code, start_date, end_date, self.kline_60min, "datetime"
            )

            # 更新日K线
            logger.info(f"开始更新股票 {code} 的日K线数据")
            self._update_kline_data(
                code, start_date, end_date, self.kline_daily, "date"
            )

        except Exception as e:

            logger.error(f"更新股票 {code} 的K线数据时出错: {e}")

    def _update_kline_data(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        kline_store: KLineStoreAggregate,
        date_field: str,
    ):
        """更新特定类型的K线数据，确保code和时间/日期唯一，避免重复更新"""
        # 从API获取K线数据
        # 从数据库获取现有数据
        db_data = kline_store.list_from_db(code, start_date, end_date)
        if db_data:
            logger.info(f"数据库中已有 {len(db_data)} 条股票 {code} 的K线数据")
            return

        api_data = kline_store.list_from_api_with_stock(code, start_date, end_date)

        if not api_data:
            return

        # 创建现有数据的唯一键集合 (code, datetime/date)
        existing_keys = set()
        for item in db_data:
            time_value = getattr(item, date_field)
            existing_keys.add((item.code, time_value))

        new_data = []
        for item in api_data:
            time_value = getattr(item, date_field)
            if (item.code, time_value) not in existing_keys:
                item.tag = MarketTag.zh_stock.value
                new_data.append(item)

        with app.orm_session() as session:
            bulk_insert(session, kline_store.model, new_data)
            session.commit()
    
    def 
    
    def list_stocks_from_api(self) -> pd.DataFrame:
        """
        获取所有股票列表
        :return: code, name, market
        """
        sh_df = self._ak_share_client.list_a_stock_quote_sh()
        sz_df = self._ak_share_client.list_a_stock_quote_sz()
        return pd.concat([sh_df, sz_df])

    def just_update_stock_quotes(self):
        """优化的股票行情数据更新方法，批量处理以提高I/O性能"""
        # 获取所有股票数据
        df = self.list_stocks_from_api()
        # 替换DataFrame中的所有NaN值为None
        df = df.replace({float("nan"): None})
        total_stocks = len(df)
        logger.info(f"开始更新 {total_stocks} 只股票的行情数据")

        with app.orm_session() as session:
            # 一次性获取所有现有股票记录
            existing_stocks = session.query(dao.ZhStock).all()

            # 创建代码到股票对象的映射，用于快速查找
            stock_map = {stock.code: stock for stock in existing_stocks}
            logger.info(f"数据库中已有 {len(stock_map)} 只股票记录")

            # 准备新增和更新的列表
            new_stocks = []
            updated_stocks = []

            # 处理每一行数据
            count = 0
            for idx, row in df.iterrows():
                # 每100条记录输出一次进度
                count += 1
                if count % 100 == 0:
                    logger.info(
                        f"处理进度: {count}/{total_stocks} ({count / total_stocks * 100:.1f}%)"
                    )

                code = row["代码"]
                asset_name = row["名称"]
                is_delisting = (
                    True if "退" in asset_name or "退市" in asset_name else False
                )
                is_st = True if "ST" in asset_name else False

                # 确保所有可能的NaN值都被替换为None
                row_dict = transform_row_2_dict(row)

                # 检查股票是否已存在
                if code in stock_map:
                    # 更新已存在的记录
                    stock = stock_map[code]
                    stock.name = row_dict["名称"]
                    stock.newest_price = row_dict["最新价"]
                    stock.price_change = row_dict["涨跌额"]
                    stock.price_change_rate = row_dict["涨跌幅"]
                    stock.turnover = row_dict["换手率"]
                    stock.volume = row_dict["成交量"]
                    stock.amount = row_dict["成交额"]
                    stock.pe_rate = row_dict["市盈率-动态"]
                    stock.market_cap = row_dict["总市值"]
                    stock.free_market_cap = row_dict["流通市值"]
                    stock.market = get_zh_stock_market(code)
                    stock.is_delisting = is_delisting
                    stock.is_st = is_st
                    updated_stocks.append(stock)
                else:
                    # 创建新记录
                    new_stock = dao.ZhStock(
                        code=code,
                        name=row_dict["名称"],
                        newest_price=row_dict["最新价"],
                        price_change=row_dict["涨跌额"],
                        price_change_rate=row_dict["涨跌幅"],
                        turnover=row_dict["换手率"],
                        volume=row_dict["成交量"],
                        amount=row_dict["成交额"],
                        pe_rate=row_dict["市盈率-动态"],
                        market_cap=row_dict["总市值"],
                        free_market_cap=row_dict["流通市值"],
                        market=get_zh_stock_market(code),
                        is_delisting=is_delisting,
                        is_st=is_st,
                    )
                    new_stocks.append(new_stock)

            # 批量添加新股票
            if new_stocks:
                logger.info(
                    f"添加 {len(new_stocks)} 只新股票, {[stock.code for stock in new_stocks]}"
                )
                # 使用通用批量插入方法
                bulk_insert(session, dao.ZhStock, new_stocks)

            # 将更新的股票添加到会话
            if updated_stocks:
                logger.info(f"更新 {len(updated_stocks)} 只现有股票")
                bulk_update_v2(session, dao.ZhStock, updated_stocks)

            session.commit()
            # 提交所有更改
            logger.info(f"股票行情数据更新完成，共处理 {total_stocks} 只股票")

    def update_single_stock_kline(
        self, code: str, start_date: datetime, end_date: datetime
    ):
        try:
            self._update_single_stock_kline(code, start_date, end_date)
        except Exception as e:
            logger.error(f"更新股票 {code} 的K线数据时出错: {e}")

    def _process_single_stock(
        self,
        code: str,
        start_date: datetime,
        end_date: datetime,
        progress_dict: Dict[str, Any],
    ) -> str:
        """处理单只股票的K线数据更新，用于并发执行"""
        try:
            logger.info(f"----- 开始更新股票 {code} 的K线数据 -----")
            self._update_single_stock_kline(code, start_date, end_date)
            logger.info(f"----- {code} 完成 -----")
            with progress_dict["lock"]:
                progress_dict["completed"] += 1
                if (
                    progress_dict["completed"] % 10 == 0
                    or progress_dict["completed"] == progress_dict["total"]
                ):
                    logger.info(
                        f"K线数据更新进度: {progress_dict['completed']}/{progress_dict['total']} ({progress_dict['completed'] / progress_dict['total'] * 100:.1f}%)"
                    )
        except Exception as e:
            error_msg = f"更新股票 {code} 的K线数据时出错: {e}"
            logger.error(error_msg)
        finally:
            time.sleep(0.7)
            return code

    def update_stock_all_info(
        self,
        start_date: datetime,
        end_date: datetime,
        is_update_quotes: bool = True,
        max_workers: int = 5,
    ):
        """并发优化的股票K线数据更新方法，提供进度跟踪和可控制的并发数量"""
        # 是否更新stock 行情数据
        if is_update_quotes:
            self.just_update_stock_quotes()

        # 获取所有股票代码
        with app.orm_session() as session:
            stocks = (
                session.query(dao.ZhStock.code)
                .filter(dao.ZhStock.is_delisting == False)
                .all()
            )
            stock_codes = [stock.code for stock in stocks]

        total_stocks = len(stock_codes)
        logger.info(
            f"开始更新 {total_stocks} 只股票的K线数据，使用 {max_workers} 个并发任务"
        )

        # 创建进度跟踪字典，使用锁来保护共享数据
        progress_dict = {
            "completed": 0,
            "total": total_stocks,
            "lock": threading.Lock(),
        }

        error_codes = []

        # 使用线程池执行器来并发处理股票更新
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务并获取future对象
            future_to_code = {
                executor.submit(
                    self._process_single_stock,
                    code,
                    start_date,
                    end_date,
                    progress_dict,
                ): code
                for code in stock_codes
            }

            # 处理完成的任务结果
            for future in concurrent.futures.as_completed(future_to_code):
                code = future_to_code[future]
                try:
                    error_code = future.result()
                    if error_code:
                        error_codes.append(error_code)
                except Exception as exc:
                    logger.error(f"处理股票 {code} 时发生异常: {exc}")
                    error_codes.append(code)

        if error_codes:
            logger.warning(f"以下股票更新失败: {error_codes}")
            communicate.sender_svc.log_alert(
                f"{self.update_stock_all_info.__name__}",
                f"以下股票更新失败: {error_codes}",
            )

        logger.info(
            f"K线数据更新完成，共处理 {total_stocks} 只股票，失败 {len(error_codes)} 只"
        )


class ZhStockFundAggregate:

    def __init__(self):
        self._ak_share_client = AkShareClient()
        self._my_client = MyClient()

    # 列出个股资金流信息
    def list_stock_fund_flow_from_api(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[dao.ZhStockFund]:
        df = self._ak_share_client.get_single_stock_fund_flow(
            code, get_zh_stock_market(code), start_date, end_date
        )

        resp: List[dao.ZhStockFund] = []
        for _, row in df.iterrows():
            resp.append(
                dao.ZhStockFund(
                    code=code,
                    date=row["日期"],
                    major_net_inflow=row["主力净流入-净额"],
                    super_net_inflow=row["超大单净流入-净额"],
                    big_net_inflow=row["大单净流入-净额"],
                    medium_net_inflow=row["中单净流入-净额"],
                    small_net_inflow=row["小单净流入-净额"],
                    major_rate=row["主力净流入-净占比"],
                    super_rate=row["超大单净流入-净占比"],
                    big_rate=row["大单净流入-净占比"],
                    medium_rate=row["中单净流入-净占比"],
                    small_rate=row["小单净流入-净占比"],
                )
            )
        return resp

    # 列出个股资金流信息
    def list_stock_fund_flow_from_db(
        self, code: str, start_date: datetime, end_date: datetime
    ) -> List[dao.ZhStockFund]:
        with app.orm_session() as session:
            return (
                session.query(dao.ZhStockFund)
                .filter(dao.ZhStockFund.code == code)
                .filter(dao.ZhStockFund.date >= start_date)
                .filter(dao.ZhStockFund.date <= end_date)
                .all()
            )

    # 列出个股融资融劵信息
    def list_stock_margin_trading_from_api(
        self, code: str, start_date: date, end_date: date
    ) -> List[dao.ZhStockMarginTrading]:
        df = self._my_client.get_margin_trading_n_securities(code)
        if df.empty:
            return []

        df = df[(df["DATE"] >= start_date) & (df["DATE"] <= end_date)]
        resp: List[dao.ZhStockMarginTrading] = []
        for _, row in df.iterrows():
            resp.append(
                dao.ZhStockMarginTrading(
                    code=code,
                    date=row["DATE"],
                    margin_balance=row["RZYE"],
                    margin_buy=row["RZMRE"],
                    margin_repay=row["RZCHE"],
                    short_balance=row["RQYL"],
                    short_sell=row["RQMCL"],
                    short_repay=row["RQCHL"],
                )
            )
        return resp

    def list_stock_margin_trading_from_db(
        self, code: str, start_date: date, end_date: date
    ) -> List[dao.ZhStockMarginTrading]:
        with app.orm_session() as session:
            return (
                session.query(dao.ZhStockMarginTrading)
                .filter(dao.ZhStockMarginTrading.code == code)
                .filter(dao.ZhStockMarginTrading.date >= start_date)
                .filter(dao.ZhStockMarginTrading.date <= end_date)
                .all()
            )

    # 列出个股盘口异动信息
    def get_stock_uma_from_api(self, code: str, date: date) -> pd.DataFrame:
        df = self._my_client.get_stock_uma(code, date)
        return df

    # 列出个股盘口异动信息
    def list_stock_uma_from_db(
        self, code: str, start_date: date, end_date: date
    ) -> List[dao.ZhStockUMA]:
        with app.orm_session() as session:
            return (
                session.query(dao.ZhStockUMA)
                .filter(dao.ZhStockUMA.code == code)
                .filter(dao.ZhStockUMA.date >= start_date)
                .filter(dao.ZhStockUMA.date <= end_date)
                .all()
            )

    # 收集某个股票一天的异动的数据
    def collect_stock_one_uma(self, code: str, date: date):
        df = self.get_stock_uma_from_api(code, date)
        if df.empty:
            return

        uma_up_type_code = PriceUMAUpAction.list_values()
        uma_down_type_code = PriceUMADownAction.list_values()
        up_type_code = []
        down_type_code = []
        uma_up_vol = 0
        uma_down_vol = 0

        for _, row in df.iterrows():
            # 辨别异动类型
            uma_code = row["t"]
            uma_vol = row["v"]

            if uma_code in uma_up_type_code:
                up_type_code.append(str(uma_code))
                uma_up_vol += uma_vol

            elif uma_code in uma_down_type_code:
                down_type_code.append(str(uma_code))
                uma_down_vol += uma_vol

        logger.info(
            f"code: {code}, date: {date}, up_type_code: {up_type_code}, down_type_code: {down_type_code}, uma_up_vol: {uma_up_vol}, uma_down_vol: {uma_down_vol}"
        )
        with app.orm_session() as session:
            data_obj = dao.ZhStockUMA(
                code=code,
                date=date,
                up_type_code=",".join(up_type_code),
                down_type_code=",".join(down_type_code),
                up_vol=uma_up_vol,
                down_vol=uma_down_vol,
            )
            session.add(data_obj)
            session.commit()

    def collect_stock_uma(self, code: str, before_days: int = 60):
        start_date, end_date = get_datetime_range(before_days)

        # 先判断数据库中是否已经存在数据
        db_data = self.list_stock_uma_from_db(code, start_date.date(), end_date.date())
        if db_data:
            logger.info(f"数据库中已有 {len(db_data)} 条股票 {code} 的盘口异动数据")
            return

        while start_date <= end_date:
            self.collect_stock_one_uma(code, start_date)
            time.sleep(2)
            start_date += timedelta(days=1)

    def collect_stock_margin_trading(self, code: str, start_date: date, end_date: date):
        # 先判断数据库中是否已经存在数据
        db_data = self.list_stock_margin_trading_from_db(code, start_date, end_date)
        if db_data:
            logger.info(f"数据库中已有 {len(db_data)} 条股票 {code} 的融资融劵数据")
            return

        api_data = self.list_stock_margin_trading_from_api(code, start_date, end_date)
        if not api_data:
            return

        with app.orm_session() as session:
            bulk_insert(session, dao.ZhStockMarginTrading, api_data)
            session.commit()

    # 收集个股资金流
    def collect_stock_fund_flow(self, code: str, before_days: int = 60):
        start_date, end_date = get_datetime_range(before_days)

        # 先判断数据库中是否已经存在数据
        db_data = self.list_stock_fund_flow_from_db(code, start_date, end_date)
        if db_data:
            logger.info(f"数据库中已有 {len(db_data)} 条股票 {code} 的资金流数据")
            return

        api_data = self.list_stock_fund_flow_from_api(code, start_date, end_date)
        if not api_data:
            return

        with app.orm_session() as session:
            bulk_insert(session, dao.ZhStockFund, api_data)
            session.commit()
