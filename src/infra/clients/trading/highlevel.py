from datetime import datetime, timedelta
from typing import Optional

from src.infra.clients.trading.ak import AkShareClient
from src.infra.clients.trading.do import StockMinuteData
from src.infra.clients.trading.utils import get_zh_latest_trading_datetime


class HighLevelTradingClient:
    
    def __init__(self):
        self.ak_cli = AkShareClient()
    
    def get_zh_stock_today_open(self, code: str) -> Optional[float]:
        today = get_zh_latest_trading_datetime()
        df = self.ak_cli.list_stock_data_wit_daily(code, today, today)
        if df.empty:
            return None
        return df.iloc[0]["开盘"]
    
    def get_zh_stock_current_change(self, code: str) -> Optional[float]:
        today = get_zh_latest_trading_datetime()
        df = self.ak_cli.list_stock_data_wit_daily(code, today, today)
        if df.empty:
            return None
        return df.iloc[0]["涨跌幅"]
    
    def get_zh_stock_current_min_data(self, code: str) -> Optional[StockMinuteData]:
        start_datetime = get_zh_latest_trading_datetime()
        end_datetime = start_datetime + timedelta(minutes=1)
        
        
        df = self.ak_cli.list_stock_data_with_minute(code, start_datetime, end_datetime)
        if df.empty:
            return None
        # 返回最后一条数据的成
        return StockMinuteData(
            code=code,
            open=df.iloc[-1]["开盘"],
            close=df.iloc[-1]["收盘"],
            high=df.iloc[-1]["最高"],
            low=df.iloc[-1]["最低"],
            volume=df.iloc[-1]["成交量"],
            amount=df.iloc[-1]["成交额"],
            time=df.iloc[-1]["时间"],
        )

