#!/usr/bin/env python3
"""
简单的测试脚本，验证 DailyRateLimiter 的基本功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.domain.monitor.aggregate.utils import DailyRateLimiter


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    limiter = DailyRateLimiter()
    alert_type = "test_alert"
    limit = 3
    
    # 设置限制
    limiter.set_daily_limit(alert_type, limit)
    print(f"设置 {alert_type} 每日限制为 {limit}")
    
    # 测试发送
    for i in range(5):  # 尝试发送5次，但限制是3次
        can_send = limiter.can_send(alert_type)
        print(f"第 {i+1} 次检查: 可以发送 = {can_send}")
        
        if can_send:
            success = limiter.record_send(alert_type)
            print(f"  记录发送: {success}")
            
            status = limiter.get_send_status(alert_type)
            print(f"  当前状态: {status['sent_count']}/{status['daily_limit']}, 剩余: {status['remaining']}")
        else:
            print("  已达到限制，无法发送")
        print()
    
    return True


def test_multiple_alert_types():
    """测试多种告警类型"""
    print("=== 测试多种告警类型 ===")
    
    limiter = DailyRateLimiter()
    
    # 设置不同的告警类型
    limiter.set_daily_limit("price_alert", 2)
    limiter.set_daily_limit("volume_alert", 3)
    
    # 发送不同类型的告警
    limiter.record_send("price_alert")
    limiter.record_send("volume_alert")
    limiter.record_send("volume_alert")
    
    # 查看所有状态
    all_status = limiter.get_all_status()
    for alert_type, status in all_status.items():
        print(f"{alert_type}: {status['sent_count']}/{status['daily_limit']} (剩余: {status['remaining']})")
    
    return True


def test_reset_functionality():
    """测试重置功能"""
    print("=== 测试重置功能 ===")
    
    limiter = DailyRateLimiter()
    alert_type = "reset_test"
    
    # 设置限制并发送
    limiter.set_daily_limit(alert_type, 2)
    limiter.record_send(alert_type)
    limiter.record_send(alert_type)
    
    print("发送前状态:")
    status = limiter.get_send_status(alert_type)
    print(f"  {status['sent_count']}/{status['daily_limit']}, 可发送: {status['can_send']}")
    
    # 重置
    limiter.reset_daily_count(alert_type)
    print("重置后状态:")
    status = limiter.get_send_status(alert_type)
    print(f"  {status['sent_count']}/{status['daily_limit']}, 可发送: {status['can_send']}")
    
    return True


def test_default_limit():
    """测试默认限制"""
    print("=== 测试默认限制 ===")
    
    limiter = DailyRateLimiter()
    alert_type = "default_test"
    
    # 不设置限制，直接查询
    status = limiter.get_send_status(alert_type)
    print(f"默认限制: {status['daily_limit']}")
    print(f"可以发送: {status['can_send']}")
    
    return True


def main():
    """运行所有测试"""
    print("开始测试 DailyRateLimiter...\n")
    
    tests = [
        test_basic_functionality,
        test_multiple_alert_types,
        test_reset_functionality,
        test_default_limit
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result:
                print("✅ 测试通过\n")
                passed += 1
            else:
                print("❌ 测试失败\n")
        except Exception as e:
            print(f"❌ 测试出错: {e}\n")
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return 0
    else:
        print("⚠️  有测试失败")
        return 1


if __name__ == "__main__":
    exit(main())
