#!/usr/bin/env python3
"""
测试 get_zh_latest_trading_datetime 函数的午休时间处理
"""
from datetime import datetime, time
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.infra.clients.trading.utils import get_zh_latest_trading_datetime, is_zh_trading_time_range


def test_trading_datetime_scenarios():
    """测试不同时间场景下的函数行为"""
    
    print("=== 测试 get_zh_latest_trading_datetime 函数 ===\n")
    
    # 模拟不同时间点的测试
    test_cases = [
        ("09:00", "盘前时间"),
        ("09:30", "上午开盘"),
        ("10:30", "上午交易中"),
        ("11:30", "上午收盘"),
        ("12:00", "午休时间"),
        ("12:30", "午休时间"),
        ("13:00", "下午开盘"),
        ("14:00", "下午交易中"),
        ("14:57", "下午收盘"),
        ("15:00", "盘后时间"),
        ("16:00", "盘后时间"),
    ]
    
    for time_str, description in test_cases:
        hour, minute = map(int, time_str.split(':'))
        test_time = time(hour, minute, 0)
        
        # 判断是否在交易时段
        is_trading = (
            (time(9, 30, 0) <= test_time <= time(11, 30, 0)) or
            (time(13, 0, 0) <= test_time <= time(14, 57, 0))
        )
        
        # 判断是否在午休时间
        is_lunch_break = time(11, 30, 0) <= test_time < time(13, 0, 0)
        
        expected_result = None
        if is_trading:
            expected_result = f"当前时间 {time_str}"
        elif is_lunch_break:
            expected_result = "上午收盘时间 11:30"
        else:
            expected_result = "当天收盘时间 15:00"
        
        print(f"时间: {time_str:>5} | {description:>8} | 预期结果: {expected_result}")
    
    print("\n=== 实际函数调用测试 ===")
    try:
        result = get_zh_latest_trading_datetime()
        print(f"当前函数返回时间: {result}")
        print(f"返回时间格式: {result.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查当前时间是否在交易时段
        current_is_trading = is_zh_trading_time_range(result)
        print(f"当前是否在交易时段: {current_is_trading}")
        
    except Exception as e:
        print(f"函数调用出错: {e}")


if __name__ == "__main__":
    test_trading_datetime_scenarios()
