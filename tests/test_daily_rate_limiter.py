import unittest
from datetime import date, timedelta
from unittest.mock import patch

from src.domain.monitor.aggregate.utils import DailyRateLimiter


class TestDailyRateLimiter(unittest.TestCase):
    
    def setUp(self):
        """每个测试前重置限制器"""
        self.limiter = DailyRateLimiter()
    
    def test_set_and_get_daily_limit(self):
        """测试设置和获取每日限制"""
        alert_type = "price_monitor"
        limit = 50
        
        # 设置限制
        self.limiter.set_daily_limit(alert_type, limit)
        
        # 检查状态
        status = self.limiter.get_send_status(alert_type)
        self.assertEqual(status["daily_limit"], limit)
        self.assertEqual(status["sent_count"], 0)
        self.assertTrue(status["can_send"])
        self.assertEqual(status["remaining"], limit)
    
    def test_can_send_and_record_send(self):
        """测试发送检查和记录功能"""
        alert_type = "volume_monitor"
        limit = 3
        
        # 设置限制
        self.limiter.set_daily_limit(alert_type, limit)
        
        # 测试发送
        for i in range(limit):
            self.assertTrue(self.limiter.can_send(alert_type))
            self.assertTrue(self.limiter.record_send(alert_type))
            
            status = self.limiter.get_send_status(alert_type)
            self.assertEqual(status["sent_count"], i + 1)
            self.assertEqual(status["remaining"], limit - i - 1)
        
        # 达到限制后不能再发送
        self.assertFalse(self.limiter.can_send(alert_type))
        self.assertFalse(self.limiter.record_send(alert_type))
    
    def test_default_limit(self):
        """测试默认限制"""
        alert_type = "new_alert"
        
        # 没有设置限制时使用默认值
        self.assertTrue(self.limiter.can_send(alert_type))
        status = self.limiter.get_send_status(alert_type)
        self.assertEqual(status["daily_limit"], 100)  # 默认限制
    
    def test_reset_daily_count(self):
        """测试重置每日计数"""
        alert_type = "test_alert"
        limit = 5
        
        # 设置限制并发送几次
        self.limiter.set_daily_limit(alert_type, limit)
        for _ in range(3):
            self.limiter.record_send(alert_type)
        
        # 检查发送计数
        status = self.limiter.get_send_status(alert_type)
        self.assertEqual(status["sent_count"], 3)
        
        # 重置计数
        self.limiter.reset_daily_count(alert_type)
        
        # 检查重置后的状态
        status = self.limiter.get_send_status(alert_type)
        self.assertEqual(status["sent_count"], 0)
        self.assertEqual(status["remaining"], limit)
    
    def test_get_all_status(self):
        """测试获取所有状态"""
        # 设置多个告警类型
        self.limiter.set_daily_limit("alert1", 10)
        self.limiter.set_daily_limit("alert2", 20)
        
        # 发送一些告警
        self.limiter.record_send("alert1")
        self.limiter.record_send("alert2")
        self.limiter.record_send("alert2")
        
        # 获取所有状态
        all_status = self.limiter.get_all_status()
        
        self.assertIn("alert1", all_status)
        self.assertIn("alert2", all_status)
        self.assertEqual(all_status["alert1"]["sent_count"], 1)
        self.assertEqual(all_status["alert2"]["sent_count"], 2)
    
    @patch('src.domain.monitor.aggregate.utils.date')
    def test_new_day_reset(self, mock_date):
        """测试新的一天自动重置"""
        alert_type = "daily_reset_test"
        limit = 5
        
        # 模拟今天
        today = date(2024, 1, 1)
        mock_date.today.return_value = today
        
        # 设置限制并发送到限制
        self.limiter.set_daily_limit(alert_type, limit)
        for _ in range(limit):
            self.limiter.record_send(alert_type)
        
        # 确认已达到限制
        self.assertFalse(self.limiter.can_send(alert_type))
        
        # 模拟第二天
        tomorrow = today + timedelta(days=1)
        mock_date.today.return_value = tomorrow
        
        # 检查是否自动重置
        self.assertTrue(self.limiter.can_send(alert_type))
        status = self.limiter.get_send_status(alert_type)
        self.assertEqual(status["sent_count"], 0)
        self.assertEqual(status["remaining"], limit)


if __name__ == '__main__':
    unittest.main()
